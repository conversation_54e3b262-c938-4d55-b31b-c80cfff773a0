{"name": "nobi-site", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"dev": "python3 -m http.server 8000", "start": "python3 -m http.server 8000", "build": "node build.js", "preview": "python3 -m http.server 8000 --directory dist", "astro:dev": "astro dev", "astro:build": "astro build", "astro:preview": "astro preview", "check": "astro check"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/mdx": "^2.0.0", "@astrojs/tailwind": "^5.0.0", "astro": "^4.0.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@types/node": "^20.16.11", "typescript": "^5.8.3"}}