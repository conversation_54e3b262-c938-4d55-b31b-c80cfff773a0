@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@600;700&family=Poppins:wght@400;500;600&family=Fira+Code:wght@400;500&display=swap');

:root {
  --background: hsl(0, 0%, 100%);
  --foreground: hsl(20, 14.3%, 4.1%);
  --muted: hsl(60, 4.8%, 95.9%);
  --muted-foreground: hsl(25, 5.3%, 44.7%);
  --popover: hsl(0, 0%, 100%);
  --popover-foreground: hsl(20, 14.3%, 4.1%);
  --card: hsl(0, 0%, 100%);
  --card-foreground: hsl(20, 14.3%, 4.1%);
  --border: hsl(20, 5.9%, 90%);
  --input: hsl(20, 5.9%, 90%);
  --primary: hsl(214, 100%, 62%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(225, 86%, 21%);
  --secondary-foreground: hsl(210, 40%, 98%);
  --accent: hsl(34, 100%, 50%);
  --accent-foreground: hsl(34, 100%, 10%);
  --destructive: hsl(0, 84.2%, 60.2%);
  --destructive-foreground: hsl(60, 9.1%, 97.8%);
  --ring: hsl(20, 14.3%, 4.1%);
  --radius: 0.5rem;
  
  /* Dark theme colors */
  --dark-900: hsl(222, 84%, 5%);
  --dark-800: hsl(222, 47%, 11%);
  --dark-700: hsl(221, 39%, 21%);
  --dark-600: hsl(224, 33%, 35%);
  --dark-500: hsl(225, 18%, 46%);
  --dark-400: hsl(220, 14%, 59%);
  --dark-300: hsl(218, 19%, 83%);
  --dark-200: hsl(218, 27%, 92%);
  --dark-100: hsl(218, 42%, 96%);
}

.dark {
  --background: hsl(222, 84%, 5%);
  --foreground: hsl(218, 42%, 96%);
  --muted: hsl(222, 47%, 11%);
  --muted-foreground: hsl(220, 14%, 59%);
  --popover: hsl(222, 47%, 11%);
  --popover-foreground: hsl(218, 42%, 96%);
  --card: hsl(222, 47%, 11%);
  --card-foreground: hsl(218, 42%, 96%);
  --border: hsl(221, 39%, 21%);
  --input: hsl(221, 39%, 21%);
  --primary: hsl(214, 100%, 62%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(222, 47%, 11%);
  --secondary-foreground: hsl(218, 42%, 96%);
  --accent: hsl(221, 39%, 21%);
  --accent-foreground: hsl(218, 42%, 96%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(218, 42%, 96%);
  --ring: hsl(224, 72%, 83%);
}

@layer base {
  * {
    border-color: var(--border);
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Poppins', sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', sans-serif;
  }
  
  .font-mono {
    font-family: 'Fira Code', monospace;
  }
}

@layer utilities {
  .gradient-text {
    background: linear-gradient(135deg, hsl(214, 100%, 62%) 0%, hsl(34, 100%, 50%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .hero-bg {
    background: radial-gradient(ellipse at top, hsla(214, 100%, 62%, 0.1) 0%, transparent 70%),
                radial-gradient(ellipse at bottom, hsla(34, 100%, 50%, 0.1) 0%, transparent 70%);
  }
  
  .project-card {
    background: linear-gradient(135deg, hsla(214, 100%, 62%, 0.1) 0%, hsla(34, 100%, 50%, 0.1) 100%);
  }
  
  .skill-badge {
    transition: all 0.3s ease;
  }
  
  .skill-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px hsla(214, 100%, 62%, 0.3);
  }
  
  .timeline-item {
    position: relative;
  }
  
  .timeline-item::before {
    content: '';
    position: absolute;
    left: -9px;
    top: 0;
    width: 18px;
    height: 18px;
    background: hsl(214, 100%, 62%);
    border-radius: 50%;
    border: 3px solid hsl(222, 84%, 5%);
  }
  
  .dark .timeline-item::before {
    border-color: hsl(222, 84%, 5%);
  }
  
  .fade-in-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease-out;
  }
  
  .fade-in-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typewriter effect */
@keyframes typewriter {
  0%, 50% { width: 0; }
  100% { width: 100%; }
}

@keyframes cursor-blink {
  0%, 50% { border-color: hsl(214, 100%, 62%); }
  51%, 100% { border-color: transparent; }
}

.typewriter {
  overflow: hidden;
  border-right: 3px solid hsl(214, 100%, 62%);
  white-space: nowrap;
  animation: typewriter 4s steps(40) infinite, cursor-blink 1s infinite;
}
