---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Resources | Nob Hokleng | Software Developer & System Architect">
  <section class="resources pt-32 pb-24 bg-light">
    <div class="container mx-auto px-5 max-w-6xl">
      <h1 class="text-4xl font-bold text-center mb-12 font-heading relative">
        Resources & RSS Feed
        <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary to-accent rounded"></span>
      </h1>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-10">
        <div class="rss-section bg-white p-8 rounded-2xl shadow-lg">
          <h2 class="text-2xl font-bold text-secondary mb-6 font-heading">My RSS Feed</h2>
          <p class="text-text mb-6 leading-relaxed">Stay updated with my latest articles, tutorials, and tech insights</p>
          
          <div class="rss-actions flex flex-col gap-4 mb-8">
            <button type="button" class="btn-secondary flex items-center justify-center gap-3 px-6 py-3 border-2 border-primary text-primary rounded-lg font-semibold hover:bg-primary/5 transition-all duration-300 hover:-translate-y-1" onclick="showRSSOptions()" aria-label="Subscribe to RSS feed">
              <i class="fas fa-rss" aria-hidden="true"></i> Subscribe to RSS
            </button>
            <button type="button" class="btn-primary flex items-center justify-center gap-3 px-6 py-3 bg-gradient-to-r from-primary to-secondary text-white rounded-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1" onclick="copyRSSUrl()" aria-label="Copy RSS feed URL">
              <i class="fas fa-copy" aria-hidden="true"></i> Copy RSS URL
            </button>
          </div>
          
          <div class="recent-posts">
            <h3 class="text-xl font-semibold text-secondary mb-4 font-heading">Recent Posts</h3>
            <div id="rss-feed-preview" class="feed-preview bg-light p-6 rounded-xl">
              <p class="text-gray-500 italic text-center">RSS feed content will be loaded here...</p>
            </div>
          </div>
        </div>
        
        <div class="bookmarks-section bg-white p-8 rounded-2xl shadow-lg">
          <h2 class="text-2xl font-bold text-secondary mb-6 font-heading">Curated Bookmarks</h2>
          
          <div class="bookmark-categories">
            <div class="category-tabs flex flex-wrap gap-2 mb-6" role="tablist" aria-label="Bookmark categories">
              <button type="button" class="tab-btn active px-4 py-2 bg-primary text-white rounded-lg font-medium transition-all duration-300 hover:bg-primary/90" onclick="showCategory('coding', this)" role="tab" aria-selected="true" aria-controls="coding">Coding</button>
              <button type="button" class="tab-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium transition-all duration-300 hover:bg-gray-300" onclick="showCategory('devops', this)" role="tab" aria-selected="false" aria-controls="devops">DevOps</button>
              <button type="button" class="tab-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium transition-all duration-300 hover:bg-gray-300" onclick="showCategory('architecture', this)" role="tab" aria-selected="false" aria-controls="architecture">Architecture</button>
              <button type="button" class="tab-btn px-4 py-2 bg-gray-200 text-gray-700 rounded-lg font-medium transition-all duration-300 hover:bg-gray-300" onclick="showCategory('career', this)" role="tab" aria-selected="false" aria-controls="career">Career</button>
            </div>
            
            <div class="bookmark-content">
              <div id="coding" class="category-content active space-y-4">
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://spring.io/guides" target="_blank" rel="noopener" class="hover:underline">Spring Guides</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Comprehensive guides for Spring Framework development</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Java</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Spring</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://refactoring.guru" target="_blank" rel="noopener" class="hover:underline">Refactoring Guru</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Design patterns and refactoring techniques explained clearly</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Design Patterns</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Best Practices</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://github.com/donnemartin/system-design-primer" target="_blank" rel="noopener" class="hover:underline">System Design Primer</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Learn how to design large-scale systems</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">System Design</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Architecture</span>
                  </div>
                </div>
              </div>
              
              <div id="devops" class="category-content hidden space-y-4">
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://docs.docker.com" target="_blank" rel="noopener" class="hover:underline">Docker Documentation</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Official Docker documentation and best practices</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Docker</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Containers</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://kubernetes.io/docs" target="_blank" rel="noopener" class="hover:underline">Kubernetes Docs</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Complete guide to container orchestration</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Kubernetes</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Orchestration</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://aws.amazon.com/architecture" target="_blank" rel="noopener" class="hover:underline">AWS Architecture Center</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Cloud architecture patterns and best practices</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">AWS</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Cloud</span>
                  </div>
                </div>
              </div>
              
              <div id="architecture" class="category-content hidden space-y-4">
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://microservices.io" target="_blank" rel="noopener" class="hover:underline">Microservices.io</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Patterns for building microservices architecture</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Microservices</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Patterns</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://martinfowler.com" target="_blank" rel="noopener" class="hover:underline">Martin Fowler's Blog</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Insights on software architecture and development</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Architecture</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Thought Leadership</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://12factor.net" target="_blank" rel="noopener" class="hover:underline">The Twelve-Factor App</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Methodology for building software-as-a-service apps</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">SaaS</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Best Practices</span>
                  </div>
                </div>
              </div>
              
              <div id="career" class="category-content hidden space-y-4">
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://staffeng.com" target="_blank" rel="noopener" class="hover:underline">StaffEng</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Stories and strategies for reaching Staff+ engineering roles</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Career Growth</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Leadership</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://www.levels.fyi" target="_blank" rel="noopener" class="hover:underline">Levels.fyi</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Compare career levels and compensation across tech companies</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Compensation</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Career Levels</span>
                  </div>
                </div>
                
                <div class="bookmark-item bg-light p-4 rounded-lg hover:shadow-md transition-all duration-300">
                  <h4 class="font-semibold text-primary mb-2"><a href="https://github.com/kamranahmedse/developer-roadmap" target="_blank" rel="noopener" class="hover:underline">Developer Roadmap</a></h4>
                  <p class="text-text text-sm mb-3 leading-relaxed">Step by step guides and paths to learn different tools or technologies</p>
                  <div class="bookmark-tags flex flex-wrap gap-2">
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Learning Path</span>
                    <span class="tag px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">Skills</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- RSS Modal -->
  <div id="rss-modal" class="rss-modal fixed inset-0 bg-black/50 z-50 hidden" role="dialog" aria-labelledby="rss-modal-title" aria-hidden="true">
    <div class="rss-modal-content bg-white rounded-2xl max-w-2xl mx-auto mt-20 p-8 m-4">
      <div class="rss-modal-header flex justify-between items-center mb-6">
        <h4 id="rss-modal-title" class="text-2xl font-bold text-secondary font-heading">Subscribe to My RSS Feed</h4>
        <button type="button" class="close-modal text-2xl text-gray-500 hover:text-gray-700" onclick="closeRSSModal()" aria-label="Close RSS subscription modal">&times;</button>
      </div>
      <div class="rss-modal-body">
        <p class="text-text mb-6">Choose your preferred way to subscribe:</p>
        <div class="rss-options space-y-6">
          <div class="rss-option">
            <h5 class="text-lg font-semibold text-secondary mb-3">📱 Mobile Apps</h5>
            <div class="rss-links flex flex-wrap gap-3">
              <a href="feedly://i/subscription/feed/https://nobhokleng.dev/rss.xml" class="rss-link flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                <i class="fas fa-external-link-alt"></i> Feedly
              </a>
              <a href="inoreader://add_feed/https://nobhokleng.dev/rss.xml" class="rss-link flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                <i class="fas fa-external-link-alt"></i> Inoreader
              </a>
            </div>
          </div>
          
          <div class="rss-option">
            <h5 class="text-lg font-semibold text-secondary mb-3">🌐 Web Readers</h5>
            <div class="rss-links flex flex-wrap gap-3">
              <a href="https://feedly.com/i/subscription/feed/https://nobhokleng.dev/rss.xml" target="_blank" rel="noopener" class="rss-link flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                <i class="fas fa-external-link-alt" aria-hidden="true"></i> Feedly Web
              </a>
              <a href="https://www.inoreader.com/feed/https://nobhokleng.dev/rss.xml" target="_blank" rel="noopener" class="rss-link flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
                <i class="fas fa-external-link-alt" aria-hidden="true"></i> Inoreader Web
              </a>
            </div>
          </div>
          
          <div class="rss-url-display">
            <label class="block font-semibold text-text mb-2">RSS Feed URL:</label>
            <div class="url-input-group flex">
              <input type="text" id="rss-url-input" value="https://nobhokleng.dev/rss.xml" readonly class="flex-1 p-3 border border-gray-300 rounded-l-lg bg-gray-50">
              <button type="button" onclick="copyFromInput()" class="copy-btn px-4 py-3 bg-primary text-white rounded-r-lg hover:bg-primary/90 transition-colors" aria-label="Copy RSS URL from input field">
                <i class="fas fa-copy" aria-hidden="true"></i>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script is:inline>
  function showCategory(categoryId, button) {
    // Hide all category contents
    document.querySelectorAll('.category-content').forEach(content => {
      content.classList.add('hidden');
      content.classList.remove('active');
    });
    
    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active', 'bg-primary', 'text-white');
      btn.classList.add('bg-gray-200', 'text-gray-700');
      btn.setAttribute('aria-selected', 'false');
    });
    
    // Show selected category
    const selectedContent = document.getElementById(categoryId);
    if (selectedContent) {
      selectedContent.classList.remove('hidden');
      selectedContent.classList.add('active');
    }
    
    // Mark button as active
    button.classList.add('active', 'bg-primary', 'text-white');
    button.classList.remove('bg-gray-200', 'text-gray-700');
    button.setAttribute('aria-selected', 'true');
  }

  function showRSSOptions() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
      modal.classList.remove('hidden');
      modal.setAttribute('aria-hidden', 'false');
    }
  }

  function closeRSSModal() {
    const modal = document.getElementById('rss-modal');
    if (modal) {
      modal.classList.add('hidden');
      modal.setAttribute('aria-hidden', 'true');
    }
  }

  function copyRSSUrl() {
    const url = 'https://nobhokleng.dev/rss.xml';
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(() => {
        alert('RSS URL copied to clipboard!');
      });
    } else {
      // Fallback for older browsers
      alert('Please copy this URL manually: ' + url);
    }
  }

  function copyFromInput() {
    const input = document.getElementById('rss-url-input');
    if (input && input instanceof HTMLInputElement) {
      input.select();
      if (navigator.clipboard) {
        navigator.clipboard.writeText(input.value).then(() => {
          alert('RSS URL copied to clipboard!');
        });
      } else {
        // Fallback
        try {
          document.execCommand('copy');
          alert('RSS URL copied to clipboard!');
        } catch (err) {
          alert('Please copy this URL manually: ' + input.value);
        }
      }
    }
  }

  // Close modal when clicking outside
  document.addEventListener('click', (e) => {
    const modal = document.getElementById('rss-modal');
    if (e.target === modal) {
      closeRSSModal();
    }
  });

  // Make functions available globally
  window.showCategory = showCategory;
  window.showRSSOptions = showRSSOptions;
  window.closeRSSModal = closeRSSModal;
  window.copyRSSUrl = copyRSSUrl;
  window.copyFromInput = copyFromInput;
</script> 