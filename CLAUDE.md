# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

NobiSite is a professional portfolio website in transition - currently a static HTML/CSS/JS site that's being migrated to a modern Astro.js + Tailwind CSS stack. The project demonstrates comprehensive planning with extensive documentation and follows professional development practices.

## Development Commands

### Current Implementation (Static Version)
```bash
# Development server
npm run dev          # Serves on http://localhost:8000 using Python
npm start           # Same as dev

# Build (simple file copying)
npm run build       # Runs custom build.js script
node build.js       # Direct build execution

# Preview built site
npm run preview     # Serves built files on http://localhost:8000
```

### Modern Stack (Astro.js - In Development)
```bash
# Astro development
npm run astro:dev   # Astro dev server with hot reload
npm run astro:build # Production build with Astro

# Type checking
npm run check       # TypeScript checking for Astro components
npx astro check     # Direct Astro check command
```

## Architecture Overview

### Current State
- **Frontend**: Static HTML, CSS with custom properties, vanilla JavaScript
- **Build Process**: Simple file copying via custom `build.js` script
- **Content**: Static HTML with interactive features (Chrome Dino game, RSS feed)

### Target Architecture (Migration in Progress)
- **Framework**: Astro.js with TypeScript for static site generation
- **Styling**: Tailwind CSS with custom design system
- **Content**: MDX with frontmatter-based collections (portfolio, resources)
- **Components**: Astro components for Header, Footer, ProjectCard
- **Type Safety**: Full TypeScript integration with strict null checks

### Content Collections Structure
```
src/content/
├── config.ts           # Zod schemas for content validation
├── portfolio/          # Project case studies (MDX)
└── resources/          # Curated bookmarks (MDX)
```

### Component Architecture
```
src/
├── components/         # Reusable Astro components
├── layouts/           # Page layouts (Layout.astro)
├── pages/             # File-based routing
└── styles/            # Global CSS + Tailwind
```

## Design System

### Tailwind Configuration
- **Primary**: #3a86ff (blue)
- **Secondary**: #0a2463 (dark blue)  
- **Accent**: #ff9e00 (orange)
- **Fonts**: Montserrat (headings), Poppins (body), Fira Code (mono)

### Path Aliases
- `@/*` maps to `src/*` for clean imports

## Content Management

### Portfolio Schema
- Required: title, publishDate, problem, solution, technologies, role, results, heroImage
- Optional: repoUrl, liveUrl

### Resources Schema  
- Required: title, url, description, category, tags

## Build Process

### Static Build (Current)
The `build.js` script copies static files to `dist/`:
- HTML files (index.html, 404.html)
- Assets (css/, js/, public/)
- RSS feed (rss.xml)

### Modern Build (Target)
Astro.js handles:
- Static site generation with file-based routing
- Asset optimization and hashing
- TypeScript compilation
- Tailwind CSS processing

## Development Notes

### Migration Status
- Static implementation complete and functional
- Astro structure partially implemented
- Content collections configured but need content migration
- Components need implementation/completion

### Quality Standards
- Target: 95+ Lighthouse performance score
- WCAG 2.1 AA accessibility compliance  
- TypeScript strict mode enabled
- Modern ES2022 target

### Documentation
Comprehensive project docs in `docs/` folder covering planning, technical specs, and migration guides.